#!/bin/bash

# Define directory paths
ROOT_DIR="$(dirname "$0")/.."
BUILD_DIR="$ROOT_DIR/src/build"
QEMU_DIR="$ROOT_DIR/src/qemu"
CONFIGURE_SCRIPT="$QEMU_DIR/configure"

# Create the build directory if it doesn't exist
if [ ! -d "$BUILD_DIR" ]; then
    echo "Creating build directory at $BUILD_DIR"
    mkdir -p "$BUILD_DIR"
fi

# Change to the build directory
cd "$BUILD_DIR" || {
    echo "Failed to change directory to $BUILD_DIR"
    exit 1
}

# Run the configure script with parameters
"$CONFIGURE_SCRIPT" --target-list=aarch64-softmmu --enable-debug

# Run make to build QEMU
echo "Running make in $BUILD_DIR"
make -j$(nproc) || {
    echo "Make failed to build QEMU"
    exit 1
}
