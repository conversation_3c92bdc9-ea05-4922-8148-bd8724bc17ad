#!/bin/bash

# Define the path to the QEMU executable
ROOT_DIR="$(dirname "$0")/.."
BUILD_DIR="$ROOT_DIR/src/build"
QEMU_DIR="$ROOT_DIR/src/qemu"
CONFIGURE_SCRIPT="$QEMU_DIR/configure.sh"
QEMU_EXECUTABLE="$BUILD_DIR/qemu-system-aarch64"

# Function to run QEMU with user input
run_qemu() {
    read -p "Enter software for Arm Cortex A72: " a72soft
    echo "A72 software: $a72soft"
    read -p "Enter software for Arm Cortex R5F: " r5fsoft
    echo "R5F software: $r5fsoft"
    "$QEMU_EXECUTABLE" -M j784s4-evm -device loader,file=$a72soft,addr=0x0,cpu-num=0 \
    -device loader,file=$r5fsoft,addr=0xA0000000,cpu-num=1 -nographic -semihosting
}

# Check if the QEMU executable exists and is executable
if [ -x "$QEMU_EXECUTABLE" ]; then
    echo "Executing QEMU: $QEMU_EXECUTABLE"
    # Add any necessary QEMU arguments here, for example:
    # "$QEMU_EXECUTABLE" -M virt -cpu cortex-a72 -m 1G -nographic
    run_qemu
else
    echo "QEMU executable not found or not executable: $QEMU_EXECUTABLE"
    echo "Start build QEMU from source"
    bash ./build.sh
    run_qemu
fi
