/* 
* TDA4VH SoC
*/
#ifndef HW_ARM_TDA4VH_SOC_H
#define HW_ARM_TDA4VH_SOC_H

#include "qom/object.h"
#include "hw/sysbus.h"
#include "hw/cpu/cluster.h"
#include "hw/arm/armv7m.h"
#include "target/arm/cpu.h"
#include "qemu/units.h"
#include "qemu/log.h"

#define TYPE_TDA4VH_SOC "tda4vh-soc"
OBJECT_DECLARE_SIMPLE_TYPE(TDA4VHSOCState, TDA4VH_SOC)

#define TDA4VH_MAX_A72_CPUS 8U
#define TDA4VH_MAX_R5F_CPUS 8U
#define TDA4VH_MAX_C7x_CPUS 4U

#define TDA4VH_DDR0_LINUX_START 0x80000000
#define TDA4VH_DDR0_MAX_LINUX_SIZE 0x20000000ull /* 512 MB */

#define TDA4VH_DDR0_RTOS_START                                                 \
  (TDA4VH_DDR0_LINUX_START + TDA4VH_DDR0_MAX_LINUX_SIZE) /* 0xA0000000 */

#define TDA4VH_DDR0_RTOS_CORE_TOTAL_SIZE 0x01000000 /* 16 MB */

#define TDA4VH_DDR0_MAX_RTOS_SIZE                                              \
  (TDA4VH_DDR0_RTOS_CORE_TOTAL_SIZE *                                          \
   (TDA4VH_MAX_R5F_CPUS + TDA4VH_MAX_C7x_CPUS)) /* 192 MB */

#define TDA4VH_DDR0_SHARED_SPACE_START                                         \
  (TDA4VH_DDR0_RTOS_START + TDA4VH_DDR0_MAX_RTOS_SIZE) /*0xAC000000*/

#define TDA4VH_DDR0_SHARED_SPACE_SIZE 0x03000000  /* 48 MB */

#define TDA4VH_DDR0_MIN_SIZE                                                   \
  (TDA4VH_DDR0_MAX_LINUX_SIZE + TDA4VH_DDR0_MAX_RTOS_SIZE +                    \
   TDA4VH_DDR0_SHARED_SPACE_SIZE) /* 752 MB */

#define TDA4VH_NUM_A72_CPUS 1U
#define TDA4VH_NUM_R5F_CPUS 1U
#define TDA4VH_NUM_C7x_DSPS 1U

#define TDA4VH_DDR0_MAX_SIZE (32ull * GiB)

#define TDA4VH_A72_CLUSTER_ID 0U
#define TDA4VH_R5F_CLUSTER_ID 1U

struct TDA4VHSOCState {
  /*< private >*/
  DeviceState parent_obj;

  /*< public >*/
  CPUClusterState a72_cluster;
  CPUClusterState r5f_cluster;
  ARMCPU a72_cpus[TDA4VH_NUM_A72_CPUS];
  ARMCPU r5f_cpus[TDA4VH_NUM_R5F_CPUS];
  /* TODO: add ISS for C71x in future*/
  // ARMCPU c7x_dsps[TDA4VH_NUM_C7x_DSPS];

  MemoryRegion rpu_sysmem[TDA4VH_NUM_R5F_CPUS];
  MemoryRegion rpu_alias[TDA4VH_NUM_R5F_CPUS];
  MemoryRegion ddr_ram;
};

#endif /* HW_ARM_TDA4VH_SOC_H */
