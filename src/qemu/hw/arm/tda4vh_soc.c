/*
* TDA4VH SoC
*/

#include "qemu/osdep.h"
#include "hw/boards.h"
#include "qom/object.h"
#include "hw/arm/boot.h"
#include "hw/arm/tda4vh_soc.h"
#include "hw/sysbus.h"
#include "hw/irq.h"
#include "hw/misc/unimp.h"
#include "qemu/error-report.h"
#include "exec/address-spaces.h"
#include <stdio.h>

static void tda4vh_create_r5f_cpus(MachineState *ms, TDA4VHSOCState *s,
                                   Error **errp) {
  int num_rpus =
      MIN((int)(ms->smp.cpus - TDA4VH_NUM_R5F_CPUS), TDA4VH_NUM_R5F_CPUS);
  if (num_rpus <= 0) {
    /* Don't create r5f_cluster object if there's nothing to put in it */
    qemu_log("WARNING: No R5F CPUs to create, returning\n");
    return;
  }

  object_initialize_child(OBJECT(s), "r5f-cluster", &s->r5f_cluster,
                          TYPE_CPU_CLUSTER);
  qdev_prop_set_uint32(DEVICE(&s->r5f_cluster), "cluster-id",
                       TDA4VH_R5F_CLUSTER_ID);

  for (uint8_t i = 0; i < num_rpus; i++) {
    g_autofree char *sysmem_name = g_strdup_printf("rpu-%d-memory", i);
    g_autofree char *alias_name = g_strdup_printf("rpu-sysmem-alias-%d", i);

    object_initialize_child(OBJECT(&s->r5f_cluster), "r5f_cpus[*]",
                            &s->r5f_cpus[i], ARM_CPU_TYPE_NAME("cortex-r5f"));

    /* TODO: temporary set power all to all core exist in SoC, improve in
     * future*/
    // printf("DEBUG: Setting R5F CPU %d to start powered-off\n", i);
    // object_property_set_bool(OBJECT(&s->r5f_cpus[i]),
    //                             "start-powered-off", true, &error_abort);

    memory_region_init(&s->rpu_sysmem[i], OBJECT(ms), sysmem_name,
                       TDA4VH_DDR0_RTOS_CORE_TOTAL_SIZE);
    memory_region_init_alias(&s->rpu_alias[i], OBJECT(ms), alias_name,
                             get_system_memory(), TDA4VH_DDR0_RTOS_START,
                             TDA4VH_DDR0_RTOS_CORE_TOTAL_SIZE);
    memory_region_add_subregion_overlap(&s->rpu_sysmem[i], 0, &s->rpu_alias[i],
                                        -1);
    object_property_set_link(OBJECT(&s->r5f_cpus[i]), "memory",
                             OBJECT(&s->rpu_sysmem[i]), &error_abort);

    /* TODO: temporary unset R5F as reset hight vector, improve in future*/
    // printf("DEBUG: Setting R5F CPU %d reset-hivecs\n", i);
    // object_property_set_bool(OBJECT(&s->r5f_cpus[i]), "reset-hivecs", true,
    //                          &error_abort);

    if (!qdev_realize(DEVICE(&s->r5f_cpus[i]), NULL, errp)) {
      printf("ERROR: Failed to realize R5F CPU %d\n", i);
    }
  }

  qdev_realize(DEVICE(&s->r5f_cluster), NULL, &error_fatal);
}

static void tda4vh_create_a72_cpus(MachineState *ms, TDA4VHSOCState *s,
                                   Error **errp) {

  object_initialize_child(OBJECT(s), "apu-cluster", &s->a72_cluster,
                          TYPE_CPU_CLUSTER);
  qdev_prop_set_uint32(DEVICE(&s->a72_cluster), "cluster-id",
                       TDA4VH_A72_CLUSTER_ID);
  int num_apus = MIN(ms->smp.cpus, TDA4VH_NUM_A72_CPUS);

  for (int i = 0; i < num_apus; i++) {
    Object *obj;
    object_initialize_child(OBJECT(&s->a72_cluster), "apu-cpu[*]",
                            &s->a72_cpus[i], ARM_CPU_TYPE_NAME("cortex-a72"));
    obj = OBJECT(&s->a72_cpus[i]);

    /* Disable TrustZone/EL3 */
    object_property_set_bool(obj, "has_el3", false, &error_abort);

    /* TODO: temporary set power all to all core exist in SoC, improve in
     * future*/
    // if (i) {
    //     /* Secondary CPUs start in powered-down state */
    //     object_property_set_bool(obj, "start-powered-off", true,
    //                              &error_abort);
    // }

    if (!qdev_realize(DEVICE(obj), NULL, &error_fatal)) {
      error_report("ERROR: Failed to realize A72 CPU %d\n", i);
    }
  }

  qdev_realize(DEVICE(&s->a72_cluster), NULL, &error_fatal);
}

static void tda4vh_soc_realize(DeviceState *dev_soc, Error **errp) {
  MachineState *ms = MACHINE(qdev_get_machine());
  TDA4VHSOCState *s = TDA4VH_SOC(dev_soc);
  Error *err = NULL;
  tda4vh_create_a72_cpus(ms, s, &err);
  if (err) {
    error_report("ERROR: Error creating A72 CPUs: %s\n", error_get_pretty(err));
    error_propagate(errp, err);
    return;
  }
  tda4vh_create_r5f_cpus(ms, s, &err);
  if (err) {
    error_report("ERROR: Error creating R5F CPUs: %s\n", error_get_pretty(err));
    error_propagate(errp, err);
    return;
  }

  memory_region_init_ram(&s->ddr_ram, OBJECT(dev_soc), "ddr-ram", 8 * GiB,
                         &err);
  if (err) {
    error_report("ERROR: Failed to init RAM: %s\n", error_get_pretty(err));
    error_propagate(errp, err);
    return;
  }
  memory_region_add_subregion(get_system_memory(), 0, &s->ddr_ram);
}

static void tda4vh_soc_class_init(ObjectClass *oc, void *data) {
  DeviceClass *dc = DEVICE_CLASS(oc);
  dc->realize = tda4vh_soc_realize;
  dc->desc = "TDA4VH SoC";
  /* Reason: Uses serial_hds in realize function, thus can't be used twice */
  dc->user_creatable = false;
}

static const TypeInfo tda4vh_soc_info = {
    .name = TYPE_TDA4VH_SOC,
    .parent = TYPE_DEVICE,
    .instance_size = sizeof(TDA4VHSOCState),
    .class_init = tda4vh_soc_class_init,
};

static void tda4vh_soc_register_types(void) {
  type_register_static(&tda4vh_soc_info);
}

type_init(tda4vh_soc_register_types)
